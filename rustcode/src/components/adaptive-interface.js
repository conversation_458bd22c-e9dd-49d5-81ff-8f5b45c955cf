import { createIcon } from '../icons/icon-library.js';

export class AdaptiveInterface {
    constructor(container) {
        this.container = container;
        this.panels = new Map();
        this.layouts = new Map();
        this.currentLayout = 'default';
        this.contextHistory = [];
        this.userPreferences = this.loadUserPreferences();
        this.adaptiveMode = localStorage.getItem('adaptive-mode') === 'true';
        this.learningMode = localStorage.getItem('interface-learning') === 'true';
        this.workflowPatterns = new Map();
        this.currentWorkflow = null;
        this.panelUsageStats = new Map();
        this.contextAwareFeatures = true;
        this.smartLayoutEnabled = localStorage.getItem('smart-layout') !== 'false';

        this.init();
    }

    init() {
        this.setupDefaultLayouts();
        this.createAdaptiveControls();
        this.startContextMonitoring();
        this.loadWorkflowPatterns();
        this.setupEventListeners();
        this.initializeSmartPanels();
    }

    setupDefaultLayouts() {
        // Default coding layout
        this.layouts.set('default', {
            name: 'Default',
            panels: {
                'file-explorer': { visible: true, position: 'left', width: '250px' },
                'editor': { visible: true, position: 'center', flex: 1 },
                'ai-assistant': { visible: false, position: 'right', width: '350px' },
                'terminal': { visible: false, position: 'bottom', height: '200px' },
                'git-panel': { visible: false, position: 'left-secondary', width: '250px' }
            },
            priority: ['editor', 'file-explorer', 'ai-assistant', 'terminal']
        });

        // AI-focused layout
        this.layouts.set('ai-focused', {
            name: 'AI Assistant',
            panels: {
                'file-explorer': { visible: true, position: 'left', width: '200px' },
                'editor': { visible: true, position: 'center', flex: 1 },
                'ai-assistant': { visible: true, position: 'right', width: '400px' },
                'terminal': { visible: false, position: 'bottom', height: '150px' },
                'git-panel': { visible: false, position: 'left-secondary', width: '200px' }
            },
            priority: ['ai-assistant', 'editor', 'file-explorer', 'terminal']
        });

        // Debug layout
        this.layouts.set('debug', {
            name: 'Debug Mode',
            panels: {
                'file-explorer': { visible: true, position: 'left', width: '200px' },
                'editor': { visible: true, position: 'center', flex: 1 },
                'debug-test-panel': { visible: true, position: 'right', width: '300px' },
                'terminal': { visible: true, position: 'bottom', height: '250px' },
                'ai-assistant': { visible: false, position: 'right-secondary', width: '300px' }
            },
            priority: ['editor', 'debug-test-panel', 'terminal', 'file-explorer']
        });

        // Git workflow layout
        this.layouts.set('git-workflow', {
            name: 'Git Workflow',
            panels: {
                'file-explorer': { visible: true, position: 'left', width: '200px' },
                'git-panel': { visible: true, position: 'left-secondary', width: '250px' },
                'editor': { visible: true, position: 'center', flex: 1 },
                'terminal': { visible: true, position: 'bottom', height: '200px' },
                'ai-assistant': { visible: false, position: 'right', width: '300px' }
            },
            priority: ['editor', 'git-panel', 'file-explorer', 'terminal']
        });

        // Full-screen coding
        this.layouts.set('focus', {
            name: 'Focus Mode',
            panels: {
                'editor': { visible: true, position: 'center', flex: 1 },
                'file-explorer': { visible: false, position: 'left', width: '250px' },
                'ai-assistant': { visible: false, position: 'right', width: '350px' },
                'terminal': { visible: false, position: 'bottom', height: '200px' },
                'git-panel': { visible: false, position: 'left-secondary', width: '250px' }
            },
            priority: ['editor']
        });
    }

    createAdaptiveControls() {
        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'adaptive-controls';
        controlsContainer.innerHTML = `
            <div class="layout-selector">
                <button class="layout-btn" id="layout-dropdown-btn" title="Layout Options">
                    ${createIcon('layout', 16)}
                    <span class="layout-name">${this.layouts.get(this.currentLayout).name}</span>
                    ${createIcon('chevron-down', 12)}
                </button>
                <div class="layout-dropdown" id="layout-dropdown">
                    ${Array.from(this.layouts.entries()).map(([key, layout]) => `
                        <div class="layout-option" data-layout="${key}">
                            ${createIcon('layout', 14)}
                            <span>${layout.name}</span>
                            ${key === this.currentLayout ? createIcon('check', 12) : ''}
                        </div>
                    `).join('')}
                    <div class="layout-divider"></div>
                    <div class="layout-option adaptive-toggle" data-action="toggle-adaptive">
                        ${createIcon('brain', 14)}
                        <span>Adaptive Mode</span>
                        <div class="toggle-switch ${this.adaptiveMode ? 'active' : ''}"></div>
                    </div>
                    <div class="layout-option" data-action="save-layout">
                        ${createIcon('save', 14)}
                        <span>Save Current Layout</span>
                    </div>
                    <div class="layout-option" data-action="reset-layout">
                        ${createIcon('refresh', 14)}
                        <span>Reset to Default</span>
                    </div>
                </div>
            </div>

            <div class="adaptive-status">
                <div class="workflow-indicator" id="workflow-indicator">
                    ${createIcon('workflow', 14)}
                    <span id="workflow-name">Default</span>
                </div>
                <div class="context-indicator" id="context-indicator">
                    ${createIcon('context', 14)}
                    <span id="context-name">Coding</span>
                </div>
            </div>
        `;

        // Insert controls into the main toolbar
        const toolbar = document.querySelector('.toolbar');
        if (toolbar) {
            toolbar.appendChild(controlsContainer);
        }

        this.setupControlsEventListeners();
    }

    setupControlsEventListeners() {
        const dropdownBtn = document.getElementById('layout-dropdown-btn');
        const dropdown = document.getElementById('layout-dropdown');

        dropdownBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            dropdown.classList.toggle('visible');
        });

        document.addEventListener('click', () => {
            dropdown?.classList.remove('visible');
        });

        dropdown?.addEventListener('click', (e) => {
            e.stopPropagation();
            const option = e.target.closest('.layout-option');
            if (!option) return;

            const layout = option.dataset.layout;
            const action = option.dataset.action;

            if (layout) {
                this.switchLayout(layout);
                dropdown.classList.remove('visible');
            } else if (action) {
                this.handleLayoutAction(action);
                dropdown.classList.remove('visible');
            }
        });
    }

    handleLayoutAction(action) {
        switch (action) {
            case 'toggle-adaptive':
                this.toggleAdaptiveMode();
                break;
            case 'save-layout':
                this.saveCurrentLayout();
                break;
            case 'reset-layout':
                this.resetToDefault();
                break;
        }
    }

    toggleAdaptiveMode() {
        this.adaptiveMode = !this.adaptiveMode;
        localStorage.setItem('adaptive-mode', this.adaptiveMode.toString());

        const toggle = document.querySelector('.adaptive-toggle .toggle-switch');
        toggle?.classList.toggle('active', this.adaptiveMode);

        if (this.adaptiveMode) {
            this.startContextMonitoring();
            this.showNotification('Adaptive mode enabled', 'success');
        } else {
            this.stopContextMonitoring();
            this.showNotification('Adaptive mode disabled', 'info');
        }
    }

    switchLayout(layoutKey) {
        if (!this.layouts.has(layoutKey)) return;

        this.currentLayout = layoutKey;
        const layout = this.layouts.get(layoutKey);

        this.applyLayout(layout);
        this.updateLayoutIndicator(layout.name);
        this.saveUserPreferences();

        this.showNotification(`Switched to ${layout.name} layout`, 'success');
    }

    applyLayout(layout) {
        // Apply panel visibility and positioning
        Object.entries(layout.panels).forEach(([panelId, config]) => {
            const panel = document.getElementById(`${panelId}-panel`) ||
                          document.getElementById(panelId);

            if (panel) {
                this.configurePanelLayout(panel, config);
            }
        });

        // Trigger layout recalculation
        this.recalculateLayout();
    }

    configurePanelLayout(panel, config) {
        // Set visibility
        panel.style.display = config.visible ? 'flex' : 'none';

        // Set dimensions
        if (config.width) panel.style.width = config.width;
        if (config.height) panel.style.height = config.height;
        if (config.flex) panel.style.flex = config.flex;

        // Add position classes
        panel.className = panel.className.replace(/position-\w+/g, '');
        if (config.position) {
            panel.classList.add(`position-${config.position}`);
        }
    }

    startContextMonitoring() {
        if (!this.adaptiveMode) return;

        // Monitor file types being edited
        this.monitorFileTypes();

        // Monitor panel usage
        this.monitorPanelUsage();

        // Monitor workflow patterns
        this.monitorWorkflowPatterns();

        // Start adaptive suggestions
        this.startAdaptiveSuggestions();
    }

    loadUserPreferences() {
        const saved = localStorage.getItem('adaptive-interface-preferences');
        return saved ? JSON.parse(saved) : {
            preferredLayouts: {},
            panelPreferences: {},
            workflowPatterns: {}
        };
    }

    saveUserPreferences() {
        localStorage.setItem('adaptive-interface-preferences', JSON.stringify(this.userPreferences));
    }

    showNotification(message, type = 'info') {
        console.log(`[Adaptive Interface] ${type.toUpperCase()}: ${message}`);
        // Will be enhanced with visual notifications
    }

    monitorFileTypes() {
        // This will be called when files are opened/switched
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    this.analyzeCurrentContext();
                }
            });
        });

        const editorContainer = document.querySelector('.editor-container');
        if (editorContainer) {
            observer.observe(editorContainer, { childList: true, subtree: true });
        }
    }

    analyzeCurrentContext() {
        const currentFile = this.getCurrentFileName();
        const fileType = this.getFileType(currentFile);
        const context = this.determineContext(fileType);

        this.updateContext(context);

        if (this.adaptiveMode) {
            this.suggestLayoutForContext(context);
        }
    }

    getCurrentFileName() {
        const activeTab = document.querySelector('.tab.active .tab-label');
        return activeTab ? activeTab.textContent : null;
    }

    getFileType(fileName) {
        if (!fileName) return 'unknown';

        const extension = fileName.split('.').pop().toLowerCase();
        const typeMap = {
            'rs': 'rust',
            'js': 'javascript',
            'ts': 'typescript',
            'py': 'python',
            'html': 'web',
            'css': 'web',
            'json': 'config',
            'md': 'documentation',
            'toml': 'config',
            'yml': 'config',
            'yaml': 'config'
        };

        return typeMap[extension] || 'text';
    }

    determineContext(fileType) {
        const contextMap = {
            'rust': 'rust-development',
            'javascript': 'web-development',
            'typescript': 'web-development',
            'python': 'python-development',
            'web': 'web-development',
            'config': 'configuration',
            'documentation': 'documentation'
        };

        return contextMap[fileType] || 'general-coding';
    }

    updateContext(context) {
        this.currentContext = context;
        this.contextHistory.push({
            context,
            timestamp: Date.now(),
            duration: 0
        });

        // Update context indicator
        const contextIndicator = document.getElementById('context-name');
        if (contextIndicator) {
            contextIndicator.textContent = this.formatContextName(context);
        }
    }

    formatContextName(context) {
        return context.split('-').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }

    suggestLayoutForContext(context) {
        const suggestions = {
            'rust-development': 'default',
            'web-development': 'default',
            'python-development': 'default',
            'configuration': 'focus',
            'documentation': 'focus'
        };

        const suggestedLayout = suggestions[context];
        if (suggestedLayout && suggestedLayout !== this.currentLayout) {
            this.showLayoutSuggestion(suggestedLayout, context);
        }
    }

    showLayoutSuggestion(layoutKey, context) {
        const layout = this.layouts.get(layoutKey);
        if (!layout) return;

        this.showNotification(
            `Switch to ${layout.name} layout for ${this.formatContextName(context)}?`,
            'suggestion'
        );
    }

    monitorPanelUsage() {
        // Track panel show/hide events
        document.addEventListener('panel-visibility-changed', (e) => {
            this.trackPanelUsage(e.detail.panelId, e.detail.action);
        });
    }

    trackPanelUsage(panelId, action) {
        if (!this.panelUsageStats.has(panelId)) {
            this.panelUsageStats.set(panelId, {
                shows: 0,
                hides: 0,
                totalTime: 0,
                lastShown: null
            });
        }

        const stats = this.panelUsageStats.get(panelId);

        if (action === 'show') {
            stats.shows++;
            stats.lastShown = Date.now();
        } else if (action === 'hide' && stats.lastShown) {
            stats.hides++;
            stats.totalTime += Date.now() - stats.lastShown;
            stats.lastShown = null;
        }
    }

    monitorWorkflowPatterns() {
        // This will learn from user behavior and suggest optimizations
        this.workflowMonitorInterval = setInterval(() => {
            this.analyzeWorkflowPatterns();
        }, 30000); // Analyze every 30 seconds
    }

    analyzeWorkflowPatterns() {
        // Analyze recent context history to detect patterns
        const recentHistory = this.contextHistory.slice(-10);
        const pattern = this.detectPattern(recentHistory);

        if (pattern && pattern !== this.currentWorkflow) {
            this.currentWorkflow = pattern;
            this.updateWorkflowIndicator(pattern);

            if (this.adaptiveMode) {
                this.optimizeForWorkflow(pattern);
            }
        }
    }

    detectPattern(history) {
        // Simple pattern detection - can be enhanced with ML
        const contexts = history.map(h => h.context);
        const uniqueContexts = [...new Set(contexts)];

        if (uniqueContexts.length === 1) {
            return `focused-${uniqueContexts[0]}`;
        } else if (uniqueContexts.includes('configuration') && uniqueContexts.includes('rust-development')) {
            return 'rust-configuration';
        } else if (uniqueContexts.length > 3) {
            return 'multi-context';
        }

        return 'general';
    }

    updateWorkflowIndicator(workflow) {
        const indicator = document.getElementById('workflow-name');
        if (indicator) {
            indicator.textContent = this.formatWorkflowName(workflow);
        }
    }

    formatWorkflowName(workflow) {
        return workflow.split('-').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }

    optimizeForWorkflow(workflow) {
        const optimizations = {
            'focused-rust-development': () => {
                this.suggestLayoutForWorkflow('default');
            },
            'rust-configuration': () => {
                this.suggestLayoutForWorkflow('default');
            },
            'multi-context': () => {
                this.suggestLayoutForWorkflow('ai-focused');
            }
        };

        const optimization = optimizations[workflow];
        if (optimization) {
            optimization();
        }
    }

    suggestLayoutForWorkflow(layoutKey) {
        if (layoutKey !== this.currentLayout) {
            const layout = this.layouts.get(layoutKey);
            this.showLayoutSuggestion(layoutKey, `workflow optimization`);
        }
    }

    startAdaptiveSuggestions() {
        // Start periodic analysis for adaptive suggestions
        this.adaptiveSuggestionsInterval = setInterval(() => {
            this.generateAdaptiveSuggestions();
        }, 60000); // Every minute
    }

    generateAdaptiveSuggestions() {
        // Analyze usage patterns and generate suggestions
        const suggestions = this.analyzeUsagePatterns();
        if (suggestions.length > 0) {
            this.showAdaptiveSuggestions(suggestions);
        }
    }

    analyzeUsagePatterns() {
        const suggestions = [];

        // Analyze panel usage
        this.panelUsageStats.forEach((stats, panelId) => {
            if (stats.shows > 5 && stats.totalTime > 300000) { // 5+ shows, 5+ minutes
                suggestions.push({
                    type: 'panel-optimization',
                    message: `Consider keeping ${panelId} panel visible by default`,
                    action: () => this.addPanelToDefaultLayout(panelId)
                });
            }
        });

        return suggestions;
    }

    showAdaptiveSuggestions(suggestions) {
        suggestions.forEach(suggestion => {
            this.showNotification(suggestion.message, 'suggestion');
        });
    }

    updateLayoutIndicator(name) {
        const indicator = document.querySelector('.layout-name');
        if (indicator) {
            indicator.textContent = name;
        }
    }

    recalculateLayout() {
        // Trigger Monaco editor resize
        if (window.monaco && window.monaco.editor) {
            window.monaco.editor.getEditors().forEach(editor => {
                editor.layout();
            });
        }

        // Trigger custom layout event
        window.dispatchEvent(new CustomEvent('layout-changed', {
            detail: { layout: this.currentLayout }
        }));
    }

    loadWorkflowPatterns() {
        const saved = localStorage.getItem('workflow-patterns');
        if (saved) {
            this.workflowPatterns = new Map(JSON.parse(saved));
        }
    }

    setupEventListeners() {
        // Listen for layout change requests
        window.addEventListener('request-layout-change', (e) => {
            this.switchLayout(e.detail.layout);
        });

        // Listen for panel visibility changes
        window.addEventListener('panel-visibility-changed', (e) => {
            this.trackPanelUsage(e.detail.panelId, e.detail.action);
        });
    }

    initializeSmartPanels() {
        this.smartPanelsEnabled = localStorage.getItem('smart-panels') !== 'false';

        if (this.smartPanelsEnabled) {
            // Initialize smart panel behaviors
            this.setupSmartPanelBehaviors();
        }
    }

    setupSmartPanelBehaviors() {
        // Auto-hide panels when not in use
        // Auto-show panels when relevant
        // Smart panel resizing
        // Context-aware panel content
    }

    saveCurrentLayout() {
        const currentConfig = this.getCurrentLayoutConfig();
        const name = prompt('Enter a name for this layout:');

        if (name && name.trim()) {
            this.layouts.set(name.toLowerCase().replace(/\s+/g, '-'), {
                name: name,
                panels: currentConfig,
                priority: this.getCurrentPanelPriority()
            });

            this.saveUserPreferences();
            this.showNotification(`Layout "${name}" saved`, 'success');
        }
    }

    getCurrentLayoutConfig() {
        const config = {};

        this.panels.forEach((panel, id) => {
            const element = document.getElementById(`${id}-panel`) || document.getElementById(id);
            if (element) {
                config[id] = {
                    visible: element.style.display !== 'none',
                    width: element.style.width || 'auto',
                    height: element.style.height || 'auto',
                    position: this.getPanelPosition(element)
                };
            }
        });

        return config;
    }

    getPanelPosition(element) {
        // Determine panel position based on classes or layout
        if (element.classList.contains('position-left')) return 'left';
        if (element.classList.contains('position-right')) return 'right';
        if (element.classList.contains('position-bottom')) return 'bottom';
        if (element.classList.contains('position-center')) return 'center';
        return 'auto';
    }

    getCurrentPanelPriority() {
        // Return current panel priority based on usage
        const priorities = Array.from(this.panelUsageStats.entries())
            .sort((a, b) => b[1].shows - a[1].shows)
            .map(([id]) => id);

        return priorities.length > 0 ? priorities : ['editor', 'file-explorer'];
    }

    resetToDefault() {
        this.switchLayout('default');
        this.showNotification('Layout reset to default', 'info');
    }

    stopContextMonitoring() {
        if (this.contextMonitorInterval) {
            clearInterval(this.contextMonitorInterval);
        }
        if (this.workflowMonitorInterval) {
            clearInterval(this.workflowMonitorInterval);
        }
        if (this.adaptiveSuggestionsInterval) {
            clearInterval(this.adaptiveSuggestionsInterval);
        }
    }

    // Panel management methods
    registerPanel(id, panel) {
        this.panels.set(id, panel);
    }

    showPanel(id) {
        const panel = this.panels.get(id);
        if (panel) {
            panel.show();
            this.trackPanelUsage(id, 'show');
        }
    }

    hidePanel(id) {
        const panel = this.panels.get(id);
        if (panel) {
            panel.hide();
            this.trackPanelUsage(id, 'hide');
        }
    }

    addPanelToDefaultLayout(panelId) {
        const defaultLayout = this.layouts.get('default');
        if (defaultLayout && !defaultLayout.panels[panelId]?.visible) {
            defaultLayout.panels[panelId] = {
                visible: true,
                position: 'auto',
                width: 'auto'
            };
            this.saveUserPreferences();
        }
    }

    // Cleanup
    destroy() {
        this.stopContextMonitoring();
        this.saveUserPreferences();
    }
}
}
