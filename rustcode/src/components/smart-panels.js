import { createIcon } from '../icons/icon-library.js';

export class SmartPanels {
    constructor(adaptiveInterface) {
        this.adaptiveInterface = adaptiveInterface;
        this.panels = new Map();
        this.panelStates = new Map();
        this.contextAwareness = true;
        this.autoHideEnabled = localStorage.getItem('smart-panels-auto-hide') !== 'false';
        this.intelligentResizing = localStorage.getItem('smart-panels-resize') !== 'false';
        this.contentPrediction = localStorage.getItem('smart-panels-prediction') !== 'false';
        this.collaborativeHints = localStorage.getItem('smart-panels-hints') !== 'false';
        
        this.init();
    }

    init() {
        this.setupPanelEnhancements();
        this.initializeContextAwareness();
        this.setupIntelligentBehaviors();
        this.createPanelControls();
        this.startPanelMonitoring();
    }

    setupPanelEnhancements() {
        // Enhance existing panels with smart features
        this.enhanceFileExplorer();
        this.enhanceAIAssistant();
        this.enhanceTerminal();
        this.enhanceGitPanel();
        this.enhanceDebugPanel();
    }

    enhanceFileExplorer() {
        const fileExplorer = document.getElementById('file-explorer-panel');
        if (!fileExplorer) return;

        this.registerSmartPanel('file-explorer', {
            element: fileExplorer,
            contextRelevance: {
                'rust-development': 0.9,
                'web-development': 0.9,
                'python-development': 0.9,
                'configuration': 0.7,
                'documentation': 0.5
            },
            autoShow: ['project-start', 'file-operations'],
            autoHide: ['focus-mode'],
            intelligentContent: this.createFileExplorerEnhancements.bind(this)
        });
    }

    enhanceAIAssistant() {
        const aiAssistant = document.getElementById('ai-assistant-panel');
        if (!aiAssistant) return;

        this.registerSmartPanel('ai-assistant', {
            element: aiAssistant,
            contextRelevance: {
                'rust-development': 0.8,
                'web-development': 0.8,
                'python-development': 0.8,
                'configuration': 0.6,
                'documentation': 0.9
            },
            autoShow: ['complex-task', 'error-detected', 'refactoring-needed'],
            autoHide: ['simple-editing'],
            intelligentContent: this.createAIAssistantEnhancements.bind(this)
        });
    }

    enhanceTerminal() {
        const terminal = document.getElementById('terminal-panel');
        if (!terminal) return;

        this.registerSmartPanel('terminal', {
            element: terminal,
            contextRelevance: {
                'rust-development': 0.7,
                'web-development': 0.6,
                'python-development': 0.8,
                'configuration': 0.5,
                'documentation': 0.3
            },
            autoShow: ['build-needed', 'test-run', 'git-operations'],
            autoHide: ['documentation-mode'],
            intelligentContent: this.createTerminalEnhancements.bind(this)
        });
    }

    enhanceGitPanel() {
        const gitPanel = document.getElementById('git-panel');
        if (!gitPanel) return;

        this.registerSmartPanel('git-panel', {
            element: gitPanel,
            contextRelevance: {
                'rust-development': 0.6,
                'web-development': 0.6,
                'python-development': 0.6,
                'configuration': 0.4,
                'documentation': 0.5
            },
            autoShow: ['git-workflow', 'merge-conflicts', 'commit-ready'],
            autoHide: ['focus-mode'],
            intelligentContent: this.createGitPanelEnhancements.bind(this)
        });
    }

    enhanceDebugPanel() {
        const debugPanel = document.getElementById('debug-test-panel');
        if (!debugPanel) return;

        this.registerSmartPanel('debug-test-panel', {
            element: debugPanel,
            contextRelevance: {
                'rust-development': 0.8,
                'web-development': 0.7,
                'python-development': 0.8,
                'configuration': 0.3,
                'documentation': 0.2
            },
            autoShow: ['debugging-session', 'test-failures', 'performance-issues'],
            autoHide: ['documentation-mode'],
            intelligentContent: this.createDebugPanelEnhancements.bind(this)
        });
    }

    registerSmartPanel(id, config) {
        this.panels.set(id, config);
        this.panelStates.set(id, {
            visible: config.element.style.display !== 'none',
            lastUsed: Date.now(),
            usageCount: 0,
            contextScore: 0,
            autoManaged: true
        });

        // Add smart panel class
        config.element.classList.add('smart-panel');
        
        // Setup panel-specific enhancements
        this.setupPanelSpecificFeatures(id, config);
    }

    setupPanelSpecificFeatures(id, config) {
        const element = config.element;
        
        // Add smart panel header
        this.addSmartPanelHeader(element, id);
        
        // Setup intelligent content if available
        if (config.intelligentContent) {
            config.intelligentContent(element);
        }
        
        // Setup context-aware behaviors
        this.setupContextAwareBehavior(id, config);
        
        // Setup auto-hide/show triggers
        this.setupAutoTriggers(id, config);
    }

    addSmartPanelHeader(element, panelId) {
        const existingHeader = element.querySelector('.panel-header');
        if (!existingHeader) return;

        // Add smart controls to existing header
        const smartControls = document.createElement('div');
        smartControls.className = 'smart-panel-controls';
        smartControls.innerHTML = `
            <button class="smart-panel-btn" data-action="auto-manage" title="Toggle Auto Management">
                ${createIcon('brain', 12)}
            </button>
            <button class="smart-panel-btn" data-action="context-aware" title="Context Awareness">
                ${createIcon('context', 12)}
            </button>
            <button class="smart-panel-btn" data-action="panel-settings" title="Panel Settings">
                ${createIcon('settings', 12)}
            </button>
        `;

        existingHeader.appendChild(smartControls);

        // Add event listeners
        smartControls.addEventListener('click', (e) => {
            const action = e.target.closest('.smart-panel-btn')?.dataset.action;
            if (action) {
                this.handleSmartPanelAction(panelId, action);
            }
        });
    }

    handleSmartPanelAction(panelId, action) {
        const state = this.panelStates.get(panelId);
        
        switch (action) {
            case 'auto-manage':
                state.autoManaged = !state.autoManaged;
                this.updatePanelAutoManagement(panelId, state.autoManaged);
                break;
            case 'context-aware':
                this.toggleContextAwareness(panelId);
                break;
            case 'panel-settings':
                this.showPanelSettings(panelId);
                break;
        }
    }

    updatePanelAutoManagement(panelId, enabled) {
        const btn = document.querySelector(`#${panelId}-panel .smart-panel-btn[data-action="auto-manage"]`);
        if (btn) {
            btn.classList.toggle('active', enabled);
            btn.title = enabled ? 'Disable Auto Management' : 'Enable Auto Management';
        }
    }

    initializeContextAwareness() {
        // Listen for context changes
        window.addEventListener('context-changed', (e) => {
            this.handleContextChange(e.detail.context);
        });

        // Monitor file changes
        window.addEventListener('file-changed', (e) => {
            this.handleFileChange(e.detail.fileName);
        });

        // Monitor user activity
        this.setupActivityMonitoring();
    }

    handleContextChange(context) {
        if (!this.contextAwareness) return;

        this.panels.forEach((config, panelId) => {
            const relevance = config.contextRelevance[context] || 0;
            const state = this.panelStates.get(panelId);
            
            if (state.autoManaged) {
                this.adjustPanelForContext(panelId, context, relevance);
            }
        });
    }

    adjustPanelForContext(panelId, context, relevance) {
        const config = this.panels.get(panelId);
        const state = this.panelStates.get(panelId);
        
        // Auto-show highly relevant panels
        if (relevance > 0.7 && !state.visible) {
            this.showPanelWithAnimation(panelId, 'context-relevant');
        }
        
        // Auto-hide less relevant panels in focus contexts
        if (relevance < 0.4 && state.visible && context.includes('focus')) {
            this.hidePanelWithAnimation(panelId, 'context-irrelevant');
        }
        
        // Update panel priority based on relevance
        this.updatePanelPriority(panelId, relevance);
    }

    setupIntelligentBehaviors() {
        if (this.intelligentResizing) {
            this.setupIntelligentResizing();
        }
        
        if (this.contentPrediction) {
            this.setupContentPrediction();
        }
        
        if (this.collaborativeHints) {
            this.setupCollaborativeHints();
        }
    }

    setupIntelligentResizing() {
        // Automatically resize panels based on content and usage
        this.resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const panelId = entry.target.id.replace('-panel', '');
                this.optimizePanelSize(panelId, entry.contentRect);
            });
        });

        this.panels.forEach((config, panelId) => {
            this.resizeObserver.observe(config.element);
        });
    }

    optimizePanelSize(panelId, contentRect) {
        const config = this.panels.get(panelId);
        const state = this.panelStates.get(panelId);
        
        if (!state.autoManaged) return;

        // Calculate optimal size based on content
        const optimalSize = this.calculateOptimalPanelSize(panelId, contentRect);
        
        // Apply size with smooth transition
        this.applyPanelSize(config.element, optimalSize);
    }

    calculateOptimalPanelSize(panelId, contentRect) {
        // Panel-specific size optimization logic
        const baseSize = {
            'file-explorer': { width: '250px', height: 'auto' },
            'ai-assistant': { width: '350px', height: 'auto' },
            'terminal': { width: 'auto', height: '200px' },
            'git-panel': { width: '250px', height: 'auto' },
            'debug-test-panel': { width: '300px', height: 'auto' }
        };

        return baseSize[panelId] || { width: 'auto', height: 'auto' };
    }

    applyPanelSize(element, size) {
        element.style.transition = 'width 0.3s ease, height 0.3s ease';
        if (size.width !== 'auto') element.style.width = size.width;
        if (size.height !== 'auto') element.style.height = size.height;
    }

    setupContentPrediction() {
        // Predict what content user might need and preload it
        this.contentPredictor = setInterval(() => {
            this.predictAndPrepareContent();
        }, 10000); // Every 10 seconds
    }

    predictAndPrepareContent() {
        const currentContext = this.adaptiveInterface.currentContext;
        const activeFile = this.adaptiveInterface.getCurrentFileName();
        
        // Predict based on context and file type
        this.panels.forEach((config, panelId) => {
            if (config.contentPredictor) {
                config.contentPredictor(currentContext, activeFile);
            }
        });
    }

    setupCollaborativeHints() {
        // Show helpful hints and suggestions
        this.hintSystem = {
            shown: new Set(),
            cooldown: new Map()
        };
        
        this.startHintMonitoring();
    }

    startHintMonitoring() {
        // Monitor for opportunities to show helpful hints
        this.hintMonitor = setInterval(() => {
            this.checkForHintOpportunities();
        }, 15000); // Every 15 seconds
    }

    checkForHintOpportunities() {
        // Check if user could benefit from panel suggestions
        const inactiveRelevantPanels = this.findInactiveRelevantPanels();
        
        inactiveRelevantPanels.forEach(panelId => {
            if (!this.hintSystem.shown.has(panelId)) {
                this.showPanelHint(panelId);
                this.hintSystem.shown.add(panelId);
            }
        });
    }

    findInactiveRelevantPanels() {
        const currentContext = this.adaptiveInterface.currentContext;
        const inactive = [];
        
        this.panels.forEach((config, panelId) => {
            const state = this.panelStates.get(panelId);
            const relevance = config.contextRelevance[currentContext] || 0;
            
            if (!state.visible && relevance > 0.6 && state.autoManaged) {
                inactive.push(panelId);
            }
        });
        
        return inactive;
    }

    showPanelHint(panelId) {
        const config = this.panels.get(panelId);
        const panelName = this.formatPanelName(panelId);
        
        this.adaptiveInterface.showNotification(
            `${panelName} panel might be helpful for your current task`,
            'suggestion'
        );
    }

    formatPanelName(panelId) {
        return panelId.split('-').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }

    // Panel-specific enhancement methods
    createFileExplorerEnhancements(element) {
        // Add smart file filtering
        this.addSmartFileFiltering(element);
        
        // Add recent files quick access
        this.addRecentFilesAccess(element);
        
        // Add project structure insights
        this.addProjectInsights(element);
    }

    createAIAssistantEnhancements(element) {
        // Add context-aware suggestions
        this.addContextAwareSuggestions(element);
        
        // Add quick action buttons
        this.addQuickActionButtons(element);
        
        // Add conversation insights
        this.addConversationInsights(element);
    }

    createTerminalEnhancements(element) {
        // Add smart command suggestions
        this.addSmartCommandSuggestions(element);
        
        // Add project-specific shortcuts
        this.addProjectShortcuts(element);
        
        // Add output analysis
        this.addOutputAnalysis(element);
    }

    createGitPanelEnhancements(element) {
        // Add smart commit suggestions
        this.addSmartCommitSuggestions(element);
        
        // Add branch insights
        this.addBranchInsights(element);
        
        // Add merge conflict helpers
        this.addMergeConflictHelpers(element);
    }

    createDebugPanelEnhancements(element) {
        // Add smart breakpoint suggestions
        this.addSmartBreakpoints(element);
        
        // Add performance insights
        this.addPerformanceInsights(element);
        
        // Add test optimization suggestions
        this.addTestOptimizations(element);
    }

    // Placeholder methods for specific enhancements
    addSmartFileFiltering(element) {}
    addRecentFilesAccess(element) {}
    addProjectInsights(element) {}
    addContextAwareSuggestions(element) {}
    addQuickActionButtons(element) {}
    addConversationInsights(element) {}
    addSmartCommandSuggestions(element) {}
    addProjectShortcuts(element) {}
    addOutputAnalysis(element) {}
    addSmartCommitSuggestions(element) {}
    addBranchInsights(element) {}
    addMergeConflictHelpers(element) {}
    addSmartBreakpoints(element) {}
    addPerformanceInsights(element) {}
    addTestOptimizations(element) {}

    // Panel visibility methods
    showPanelWithAnimation(panelId, reason) {
        const config = this.panels.get(panelId);
        const state = this.panelStates.get(panelId);
        
        if (config && !state.visible) {
            config.element.style.display = 'flex';
            config.element.classList.add('panel-slide-in');
            state.visible = true;
            state.lastUsed = Date.now();
            
            // Dispatch event
            window.dispatchEvent(new CustomEvent('panel-visibility-changed', {
                detail: { panelId, action: 'show', reason }
            }));
        }
    }

    hidePanelWithAnimation(panelId, reason) {
        const config = this.panels.get(panelId);
        const state = this.panelStates.get(panelId);
        
        if (config && state.visible) {
            config.element.classList.add('panel-slide-out');
            
            setTimeout(() => {
                config.element.style.display = 'none';
                config.element.classList.remove('panel-slide-out');
                state.visible = false;
            }, 300);
            
            // Dispatch event
            window.dispatchEvent(new CustomEvent('panel-visibility-changed', {
                detail: { panelId, action: 'hide', reason }
            }));
        }
    }

    updatePanelPriority(panelId, relevance) {
        const state = this.panelStates.get(panelId);
        state.contextScore = relevance;
        
        // Update visual priority indicator
        this.updatePriorityIndicator(panelId, relevance);
    }

    updatePriorityIndicator(panelId, relevance) {
        const element = this.panels.get(panelId)?.element;
        if (!element) return;
        
        // Add relevance class
        element.className = element.className.replace(/relevance-\w+/g, '');
        
        if (relevance > 0.8) {
            element.classList.add('relevance-high');
        } else if (relevance > 0.5) {
            element.classList.add('relevance-medium');
        } else {
            element.classList.add('relevance-low');
        }
    }

    // Cleanup
    destroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        
        if (this.contentPredictor) {
            clearInterval(this.contentPredictor);
        }
        
        if (this.hintMonitor) {
            clearInterval(this.hintMonitor);
        }
    }
}
