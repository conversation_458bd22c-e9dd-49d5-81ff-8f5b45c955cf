/* Enhanced UI Styles for Adaptive Interface */

/* Adaptive Controls */
.adaptive-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 0 16px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    min-height: 40px;
}

.layout-selector {
    position: relative;
}

.layout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

.layout-btn:hover {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.layout-name {
    font-weight: 500;
}

.layout-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 220px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    margin-top: 4px;
}

.layout-dropdown.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.layout-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 14px;
    cursor: pointer;
    font-size: 13px;
    color: var(--text-primary);
    transition: background-color 0.15s ease;
    border-radius: 4px;
    margin: 4px;
}

.layout-option:hover {
    background: var(--bg-hover);
}

.layout-option:first-child {
    margin-top: 8px;
}

.layout-option:last-child {
    margin-bottom: 8px;
}

.layout-divider {
    height: 1px;
    background: var(--border-color);
    margin: 8px 12px;
}

.adaptive-toggle {
    justify-content: space-between;
}

.toggle-switch {
    width: 32px;
    height: 16px;
    background: var(--bg-tertiary);
    border-radius: 8px;
    position: relative;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    transition: transform 0.2s ease;
}

.toggle-switch.active {
    background: var(--accent-color);
}

.toggle-switch.active::after {
    transform: translateX(16px);
}

/* Adaptive Status */
.adaptive-status {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: auto;
}

.workflow-indicator,
.context-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    font-size: 12px;
    color: var(--text-secondary);
}

.workflow-indicator span,
.context-indicator span {
    font-weight: 500;
}

/* Notifications System */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-width: 400px;
}

.notification {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    animation: slideIn 0.3s ease forwards;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-info {
    border-left: 4px solid var(--info-color);
}

.notification-suggestion {
    border-left: 4px solid var(--accent-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(var(--accent-color-rgb), 0.05) 100%);
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    flex: 1;
}

.notification-message {
    font-size: 14px;
    line-height: 1.4;
    color: var(--text-primary);
}

.notification-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.notification-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.notification-btn:hover {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.notification-btn:first-child {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.notification-btn:first-child:hover {
    background: var(--accent-color-dark);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    margin-left: auto;
}

.notification-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* Smart Panel Positioning */
.position-left {
    order: 1;
}

.position-left-secondary {
    order: 2;
}

.position-center {
    order: 3;
    flex: 1;
}

.position-right-secondary {
    order: 4;
}

.position-right {
    order: 5;
}

.position-bottom {
    order: 6;
    width: 100% !important;
}

/* Enhanced Panel Transitions */
.panel-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.panel-slide-in {
    animation: panelSlideIn 0.3s ease forwards;
}

.panel-slide-out {
    animation: panelSlideOut 0.3s ease forwards;
}

@keyframes panelSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes panelSlideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-20px);
    }
}

/* Context-Aware Styling */
.context-rust-development {
    --context-color: #ce422b;
}

.context-web-development {
    --context-color: #f7df1e;
}

.context-python-development {
    --context-color: #3776ab;
}

.context-configuration {
    --context-color: #6c757d;
}

.context-documentation {
    --context-color: #28a745;
}

/* Workflow Indicators */
.workflow-focused .workflow-indicator {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-color-light));
    color: white;
}

.workflow-multi-context .context-indicator {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-color-light));
    color: white;
}

/* Responsive Adaptations */
@media (max-width: 768px) {
    .adaptive-controls {
        padding: 0 12px;
        gap: 12px;
    }
    
    .layout-dropdown {
        min-width: 200px;
    }
    
    .adaptive-status {
        display: none;
    }
    
    .notifications-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .notification {
        padding: 12px;
    }
}

/* Dark Theme Enhancements */
@media (prefers-color-scheme: dark) {
    .notification {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }
    
    .layout-dropdown {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .layout-btn,
    .notification,
    .layout-dropdown {
        border-width: 2px;
    }
    
    .toggle-switch {
        border: 2px solid var(--text-primary);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .notification,
    .layout-dropdown,
    .panel-transition,
    .toggle-switch,
    .toggle-switch::after {
        transition: none;
        animation: none;
    }
}
