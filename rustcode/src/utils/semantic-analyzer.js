export class SemanticAnalyzer {
    constructor() {
        this.codebase = new Map(); // File path -> AST and metadata
        this.dependencyGraph = new Map(); // File -> dependencies
        this.symbolTable = new Map(); // Symbol -> locations and references
        this.typeInformation = new Map(); // Symbol -> type information
        this.codeMetrics = new Map(); // File -> quality metrics
        this.analysisCache = new Map(); // Cache for expensive operations
        this.languageSupport = new Map();
        
        this.initializeLanguageSupport();
    }

    initializeLanguageSupport() {
        // Rust language support
        this.languageSupport.set('rust', {
            extensions: ['.rs'],
            keywords: ['fn', 'let', 'mut', 'struct', 'enum', 'impl', 'trait', 'mod', 'use', 'pub'],
            patterns: {
                function: /fn\s+(\w+)\s*\(/g,
                struct: /struct\s+(\w+)/g,
                enum: /enum\s+(\w+)/g,
                trait: /trait\s+(\w+)/g,
                impl: /impl\s+(?:<[^>]*>\s+)?(\w+)/g,
                use: /use\s+([^;]+);/g,
                mod: /mod\s+(\w+)/g
            },
            analyzer: this.analyzeRustCode.bind(this)
        });

        // JavaScript/TypeScript support
        this.languageSupport.set('javascript', {
            extensions: ['.js', '.ts', '.jsx', '.tsx'],
            keywords: ['function', 'class', 'const', 'let', 'var', 'import', 'export', 'interface', 'type'],
            patterns: {
                function: /(?:function\s+(\w+)|(\w+)\s*=\s*(?:function|\([^)]*\)\s*=>))/g,
                class: /class\s+(\w+)/g,
                interface: /interface\s+(\w+)/g,
                type: /type\s+(\w+)/g,
                import: /import\s+(?:{[^}]+}|\w+)\s+from\s+['"]([^'"]+)['"]/g,
                export: /export\s+(?:default\s+)?(?:function|class|const|let|var)\s+(\w+)/g
            },
            analyzer: this.analyzeJavaScriptCode.bind(this)
        });

        // Python support
        this.languageSupport.set('python', {
            extensions: ['.py'],
            keywords: ['def', 'class', 'import', 'from', 'async', 'await', 'lambda'],
            patterns: {
                function: /def\s+(\w+)\s*\(/g,
                class: /class\s+(\w+)/g,
                import: /(?:import\s+(\w+)|from\s+(\w+)\s+import)/g
            },
            analyzer: this.analyzePythonCode.bind(this)
        });
    }

    async analyzeFile(filePath, content) {
        const language = this.detectLanguage(filePath);
        const support = this.languageSupport.get(language);
        
        if (!support) {
            return this.analyzeGenericCode(filePath, content);
        }

        const analysis = await support.analyzer(filePath, content);
        this.codebase.set(filePath, analysis);
        
        // Update dependency graph
        this.updateDependencyGraph(filePath, analysis.dependencies);
        
        // Update symbol table
        this.updateSymbolTable(filePath, analysis.symbols);
        
        // Calculate code metrics
        this.calculateCodeMetrics(filePath, content, analysis);
        
        return analysis;
    }

    detectLanguage(filePath) {
        const extension = filePath.split('.').pop().toLowerCase();
        
        for (const [language, support] of this.languageSupport) {
            if (support.extensions.some(ext => ext.includes(extension))) {
                return language;
            }
        }
        
        return 'generic';
    }

    async analyzeRustCode(filePath, content) {
        const analysis = {
            language: 'rust',
            symbols: [],
            dependencies: [],
            functions: [],
            structs: [],
            enums: [],
            traits: [],
            implementations: [],
            modules: [],
            uses: [],
            errors: [],
            warnings: [],
            suggestions: []
        };

        const support = this.languageSupport.get('rust');
        
        // Extract functions
        let match;
        while ((match = support.patterns.function.exec(content)) !== null) {
            analysis.functions.push({
                name: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index),
                visibility: this.extractVisibility(content, match.index),
                parameters: this.extractFunctionParameters(content, match.index),
                returnType: this.extractReturnType(content, match.index)
            });
        }

        // Extract structs
        support.patterns.struct.lastIndex = 0;
        while ((match = support.patterns.struct.exec(content)) !== null) {
            analysis.structs.push({
                name: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index),
                visibility: this.extractVisibility(content, match.index),
                fields: this.extractStructFields(content, match.index)
            });
        }

        // Extract enums
        support.patterns.enum.lastIndex = 0;
        while ((match = support.patterns.enum.exec(content)) !== null) {
            analysis.enums.push({
                name: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index),
                visibility: this.extractVisibility(content, match.index),
                variants: this.extractEnumVariants(content, match.index)
            });
        }

        // Extract traits
        support.patterns.trait.lastIndex = 0;
        while ((match = support.patterns.trait.exec(content)) !== null) {
            analysis.traits.push({
                name: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index),
                visibility: this.extractVisibility(content, match.index),
                methods: this.extractTraitMethods(content, match.index)
            });
        }

        // Extract implementations
        support.patterns.impl.lastIndex = 0;
        while ((match = support.patterns.impl.exec(content)) !== null) {
            analysis.implementations.push({
                target: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index),
                methods: this.extractImplMethods(content, match.index)
            });
        }

        // Extract use statements (dependencies)
        support.patterns.use.lastIndex = 0;
        while ((match = support.patterns.use.exec(content)) !== null) {
            const dependency = this.parseRustUse(match[1]);
            analysis.uses.push(dependency);
            analysis.dependencies.push(dependency.module);
        }

        // Extract modules
        support.patterns.mod.lastIndex = 0;
        while ((match = support.patterns.mod.exec(content)) !== null) {
            analysis.modules.push({
                name: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index),
                visibility: this.extractVisibility(content, match.index)
            });
        }

        // Combine all symbols
        analysis.symbols = [
            ...analysis.functions.map(f => ({ ...f, type: 'function' })),
            ...analysis.structs.map(s => ({ ...s, type: 'struct' })),
            ...analysis.enums.map(e => ({ ...e, type: 'enum' })),
            ...analysis.traits.map(t => ({ ...t, type: 'trait' })),
            ...analysis.modules.map(m => ({ ...m, type: 'module' }))
        ];

        // Analyze code quality
        this.analyzeRustQuality(content, analysis);

        return analysis;
    }

    async analyzeJavaScriptCode(filePath, content) {
        const analysis = {
            language: 'javascript',
            symbols: [],
            dependencies: [],
            functions: [],
            classes: [],
            interfaces: [],
            types: [],
            imports: [],
            exports: [],
            errors: [],
            warnings: [],
            suggestions: []
        };

        const support = this.languageSupport.get('javascript');
        
        // Extract functions
        let match;
        while ((match = support.patterns.function.exec(content)) !== null) {
            const name = match[1] || match[2];
            if (name) {
                analysis.functions.push({
                    name,
                    line: this.getLineNumber(content, match.index),
                    column: this.getColumnNumber(content, match.index),
                    isAsync: content.substring(Math.max(0, match.index - 10), match.index).includes('async'),
                    parameters: this.extractJSFunctionParameters(content, match.index)
                });
            }
        }

        // Extract classes
        support.patterns.class.lastIndex = 0;
        while ((match = support.patterns.class.exec(content)) !== null) {
            analysis.classes.push({
                name: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index),
                methods: this.extractClassMethods(content, match.index),
                extends: this.extractClassExtends(content, match.index)
            });
        }

        // Extract imports
        support.patterns.import.lastIndex = 0;
        while ((match = support.patterns.import.exec(content)) !== null) {
            analysis.imports.push({
                module: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index)
            });
            analysis.dependencies.push(match[1]);
        }

        // TypeScript specific patterns
        if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
            this.analyzeTypeScriptSpecific(content, analysis);
        }

        // Combine symbols
        analysis.symbols = [
            ...analysis.functions.map(f => ({ ...f, type: 'function' })),
            ...analysis.classes.map(c => ({ ...c, type: 'class' })),
            ...analysis.interfaces.map(i => ({ ...i, type: 'interface' })),
            ...analysis.types.map(t => ({ ...t, type: 'type' }))
        ];

        // Analyze code quality
        this.analyzeJavaScriptQuality(content, analysis);

        return analysis;
    }

    async analyzePythonCode(filePath, content) {
        const analysis = {
            language: 'python',
            symbols: [],
            dependencies: [],
            functions: [],
            classes: [],
            imports: [],
            errors: [],
            warnings: [],
            suggestions: []
        };

        const support = this.languageSupport.get('python');
        
        // Extract functions
        let match;
        while ((match = support.patterns.function.exec(content)) !== null) {
            analysis.functions.push({
                name: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index),
                isAsync: content.substring(Math.max(0, match.index - 10), match.index).includes('async'),
                parameters: this.extractPythonFunctionParameters(content, match.index)
            });
        }

        // Extract classes
        support.patterns.class.lastIndex = 0;
        while ((match = support.patterns.class.exec(content)) !== null) {
            analysis.classes.push({
                name: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index),
                methods: this.extractPythonClassMethods(content, match.index),
                inheritance: this.extractPythonInheritance(content, match.index)
            });
        }

        // Extract imports
        support.patterns.import.lastIndex = 0;
        while ((match = support.patterns.import.exec(content)) !== null) {
            const module = match[1] || match[2];
            if (module) {
                analysis.imports.push({
                    module,
                    line: this.getLineNumber(content, match.index),
                    column: this.getColumnNumber(content, match.index)
                });
                analysis.dependencies.push(module);
            }
        }

        // Combine symbols
        analysis.symbols = [
            ...analysis.functions.map(f => ({ ...f, type: 'function' })),
            ...analysis.classes.map(c => ({ ...c, type: 'class' }))
        ];

        // Analyze code quality
        this.analyzePythonQuality(content, analysis);

        return analysis;
    }

    analyzeGenericCode(filePath, content) {
        return {
            language: 'generic',
            symbols: [],
            dependencies: [],
            errors: [],
            warnings: [],
            suggestions: [],
            lineCount: content.split('\n').length,
            characterCount: content.length
        };
    }

    // Helper methods for code analysis
    getLineNumber(content, index) {
        return content.substring(0, index).split('\n').length;
    }

    getColumnNumber(content, index) {
        const lines = content.substring(0, index).split('\n');
        return lines[lines.length - 1].length + 1;
    }

    extractVisibility(content, index) {
        const beforeMatch = content.substring(Math.max(0, index - 20), index);
        if (beforeMatch.includes('pub(crate)')) return 'pub(crate)';
        if (beforeMatch.includes('pub')) return 'pub';
        return 'private';
    }

    extractFunctionParameters(content, index) {
        // Extract parameters from function signature
        const match = content.substring(index).match(/\(([^)]*)\)/);
        if (!match) return [];
        
        return match[1].split(',').map(param => param.trim()).filter(p => p);
    }

    extractReturnType(content, index) {
        // Extract return type from function signature
        const match = content.substring(index).match(/\)\s*->\s*([^{]+)/);
        return match ? match[1].trim() : 'void';
    }

    extractStructFields(content, index) {
        // Extract struct fields - simplified implementation
        const structMatch = content.substring(index).match(/struct\s+\w+\s*{([^}]*)}/);
        if (!structMatch) return [];
        
        return structMatch[1].split(',').map(field => field.trim()).filter(f => f);
    }

    extractEnumVariants(content, index) {
        // Extract enum variants - simplified implementation
        const enumMatch = content.substring(index).match(/enum\s+\w+\s*{([^}]*)}/);
        if (!enumMatch) return [];
        
        return enumMatch[1].split(',').map(variant => variant.trim()).filter(v => v);
    }

    extractTraitMethods(content, index) {
        // Extract trait methods - simplified implementation
        return [];
    }

    extractImplMethods(content, index) {
        // Extract implementation methods - simplified implementation
        return [];
    }

    parseRustUse(useStatement) {
        // Parse Rust use statement
        const parts = useStatement.split('::');
        return {
            module: parts[0].trim(),
            items: parts.slice(1),
            full: useStatement.trim()
        };
    }

    extractJSFunctionParameters(content, index) {
        // Extract JavaScript function parameters
        const match = content.substring(index).match(/\(([^)]*)\)/);
        if (!match) return [];
        
        return match[1].split(',').map(param => param.trim()).filter(p => p);
    }

    extractClassMethods(content, index) {
        // Extract class methods - simplified implementation
        return [];
    }

    extractClassExtends(content, index) {
        // Extract class inheritance
        const match = content.substring(index).match(/class\s+\w+\s+extends\s+(\w+)/);
        return match ? match[1] : null;
    }

    analyzeTypeScriptSpecific(content, analysis) {
        const support = this.languageSupport.get('javascript');
        
        // Extract interfaces
        let match;
        while ((match = support.patterns.interface.exec(content)) !== null) {
            analysis.interfaces.push({
                name: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index)
            });
        }

        // Extract type aliases
        support.patterns.type.lastIndex = 0;
        while ((match = support.patterns.type.exec(content)) !== null) {
            analysis.types.push({
                name: match[1],
                line: this.getLineNumber(content, match.index),
                column: this.getColumnNumber(content, match.index)
            });
        }
    }

    extractPythonFunctionParameters(content, index) {
        // Extract Python function parameters
        const match = content.substring(index).match(/\(([^)]*)\)/);
        if (!match) return [];
        
        return match[1].split(',').map(param => param.trim()).filter(p => p);
    }

    extractPythonClassMethods(content, index) {
        // Extract Python class methods - simplified implementation
        return [];
    }

    extractPythonInheritance(content, index) {
        // Extract Python class inheritance
        const match = content.substring(index).match(/class\s+\w+\(([^)]+)\)/);
        return match ? match[1].split(',').map(c => c.trim()) : [];
    }

    // Code quality analysis methods
    analyzeRustQuality(content, analysis) {
        // Analyze Rust-specific quality metrics
        this.checkRustBestPractices(content, analysis);
        this.checkRustPerformance(content, analysis);
        this.checkRustSafety(content, analysis);
    }

    analyzeJavaScriptQuality(content, analysis) {
        // Analyze JavaScript-specific quality metrics
        this.checkJavaScriptBestPractices(content, analysis);
        this.checkJavaScriptPerformance(content, analysis);
        this.checkJavaScriptSecurity(content, analysis);
    }

    analyzePythonQuality(content, analysis) {
        // Analyze Python-specific quality metrics
        this.checkPythonBestPractices(content, analysis);
        this.checkPythonPerformance(content, analysis);
        this.checkPythonStyle(content, analysis);
    }

    checkRustBestPractices(content, analysis) {
        // Check for Rust best practices
        if (content.includes('unwrap()')) {
            analysis.warnings.push({
                type: 'best-practice',
                message: 'Consider using proper error handling instead of unwrap()',
                severity: 'medium'
            });
        }
    }

    checkRustPerformance(content, analysis) {
        // Check for Rust performance issues
        if (content.includes('clone()') && content.split('clone()').length > 5) {
            analysis.suggestions.push({
                type: 'performance',
                message: 'Consider using references instead of cloning',
                severity: 'low'
            });
        }
    }

    checkRustSafety(content, analysis) {
        // Check for Rust safety issues
        if (content.includes('unsafe')) {
            analysis.warnings.push({
                type: 'safety',
                message: 'Unsafe code detected - ensure proper safety guarantees',
                severity: 'high'
            });
        }
    }

    checkJavaScriptBestPractices(content, analysis) {
        // Check for JavaScript best practices
        if (content.includes('var ')) {
            analysis.suggestions.push({
                type: 'best-practice',
                message: 'Consider using let or const instead of var',
                severity: 'low'
            });
        }
    }

    checkJavaScriptPerformance(content, analysis) {
        // Check for JavaScript performance issues
        if (content.includes('document.getElementById') && content.split('document.getElementById').length > 3) {
            analysis.suggestions.push({
                type: 'performance',
                message: 'Consider caching DOM queries',
                severity: 'medium'
            });
        }
    }

    checkJavaScriptSecurity(content, analysis) {
        // Check for JavaScript security issues
        if (content.includes('eval(')) {
            analysis.warnings.push({
                type: 'security',
                message: 'eval() usage detected - potential security risk',
                severity: 'high'
            });
        }
    }

    checkPythonBestPractices(content, analysis) {
        // Check for Python best practices
        if (content.includes('import *')) {
            analysis.warnings.push({
                type: 'best-practice',
                message: 'Avoid wildcard imports',
                severity: 'medium'
            });
        }
    }

    checkPythonPerformance(content, analysis) {
        // Check for Python performance issues
        if (content.includes('for ') && content.includes('range(len(')) {
            analysis.suggestions.push({
                type: 'performance',
                message: 'Consider using enumerate() instead of range(len())',
                severity: 'low'
            });
        }
    }

    checkPythonStyle(content, analysis) {
        // Check for Python style issues (PEP 8)
        const lines = content.split('\n');
        lines.forEach((line, index) => {
            if (line.length > 79) {
                analysis.suggestions.push({
                    type: 'style',
                    message: `Line ${index + 1} exceeds 79 characters`,
                    severity: 'low',
                    line: index + 1
                });
            }
        });
    }

    // Dependency and symbol management
    updateDependencyGraph(filePath, dependencies) {
        this.dependencyGraph.set(filePath, dependencies);
    }

    updateSymbolTable(filePath, symbols) {
        symbols.forEach(symbol => {
            if (!this.symbolTable.has(symbol.name)) {
                this.symbolTable.set(symbol.name, []);
            }
            
            this.symbolTable.get(symbol.name).push({
                file: filePath,
                line: symbol.line,
                column: symbol.column,
                type: symbol.type
            });
        });
    }

    calculateCodeMetrics(filePath, content, analysis) {
        const lines = content.split('\n');
        const nonEmptyLines = lines.filter(line => line.trim().length > 0);
        const commentLines = lines.filter(line => {
            const trimmed = line.trim();
            return trimmed.startsWith('//') || trimmed.startsWith('#') || 
                   trimmed.startsWith('/*') || trimmed.startsWith('*');
        });

        const metrics = {
            totalLines: lines.length,
            codeLines: nonEmptyLines.length,
            commentLines: commentLines.length,
            commentRatio: commentLines.length / nonEmptyLines.length,
            complexity: this.calculateComplexity(content),
            maintainabilityIndex: this.calculateMaintainabilityIndex(analysis),
            technicalDebt: this.calculateTechnicalDebt(analysis)
        };

        this.codeMetrics.set(filePath, metrics);
    }

    calculateComplexity(content) {
        // Simplified cyclomatic complexity calculation
        const complexityKeywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', 'match'];
        let complexity = 1; // Base complexity
        
        complexityKeywords.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'g');
            const matches = content.match(regex);
            if (matches) {
                complexity += matches.length;
            }
        });
        
        return complexity;
    }

    calculateMaintainabilityIndex(analysis) {
        // Simplified maintainability index
        const errorWeight = analysis.errors.length * 10;
        const warningWeight = analysis.warnings.length * 5;
        const suggestionWeight = analysis.suggestions.length * 2;
        
        return Math.max(0, 100 - errorWeight - warningWeight - suggestionWeight);
    }

    calculateTechnicalDebt(analysis) {
        // Calculate technical debt score
        const highSeverityIssues = analysis.errors.length + 
                                 analysis.warnings.filter(w => w.severity === 'high').length;
        const mediumSeverityIssues = analysis.warnings.filter(w => w.severity === 'medium').length;
        const lowSeverityIssues = analysis.suggestions.length;
        
        return highSeverityIssues * 8 + mediumSeverityIssues * 4 + lowSeverityIssues * 1;
    }

    // Query methods
    getSymbolReferences(symbolName) {
        return this.symbolTable.get(symbolName) || [];
    }

    getFileDependencies(filePath) {
        return this.dependencyGraph.get(filePath) || [];
    }

    getCodeMetrics(filePath) {
        return this.codeMetrics.get(filePath);
    }

    getFileAnalysis(filePath) {
        return this.codebase.get(filePath);
    }

    // Search and query capabilities
    searchSymbols(query) {
        const results = [];
        
        this.symbolTable.forEach((locations, symbolName) => {
            if (symbolName.toLowerCase().includes(query.toLowerCase())) {
                results.push({
                    symbol: symbolName,
                    locations: locations
                });
            }
        });
        
        return results;
    }

    findDefinition(symbolName, currentFile) {
        const references = this.getSymbolReferences(symbolName);
        
        // Prefer definitions in the current file, then in dependencies
        const currentFileRefs = references.filter(ref => ref.file === currentFile);
        if (currentFileRefs.length > 0) {
            return currentFileRefs[0];
        }
        
        return references[0] || null;
    }

    findReferences(symbolName) {
        return this.getSymbolReferences(symbolName);
    }

    // Cleanup
    clearCache() {
        this.analysisCache.clear();
    }

    removeFile(filePath) {
        this.codebase.delete(filePath);
        this.dependencyGraph.delete(filePath);
        this.codeMetrics.delete(filePath);
        
        // Remove symbols from this file
        this.symbolTable.forEach((locations, symbolName) => {
            const filtered = locations.filter(loc => loc.file !== filePath);
            if (filtered.length === 0) {
                this.symbolTable.delete(symbolName);
            } else {
                this.symbolTable.set(symbolName, filtered);
            }
        });
    }
}
