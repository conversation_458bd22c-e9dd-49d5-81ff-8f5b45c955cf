// Test script to verify file explorer error fixes
console.log('🔧 Testing File Explorer Error Fixes...');

function testFileExplorerErrors() {
    console.log('\n=== File Explorer Error Fix Test ===');
    
    // Test 1: Check if FileExplorer class is available
    console.log('\n1. Testing FileExplorer class availability...');
    try {
        if (window.rustCodeApp && window.rustCodeApp.fileExplorer) {
            console.log('✅ FileExplorer instance found');
            
            // Test if the missing methods are now available
            const fileExplorer = window.rustCodeApp.fileExplorer;
            
            if (typeof fileExplorer.updatePerformanceMetrics === 'function') {
                console.log('✅ updatePerformanceMetrics method exists');
            } else {
                console.log('❌ updatePerformanceMetrics method missing');
            }
            
            if (typeof fileExplorer.hideContextMenu === 'function') {
                console.log('✅ hideContextMenu method exists');
            } else {
                console.log('❌ hideContextMenu method missing');
            }
            
            if (typeof fileExplorer.throttle === 'function') {
                console.log('✅ throttle method exists');
            } else {
                console.log('❌ throttle method missing');
            }
            
        } else {
            console.log('❌ FileExplorer instance not found');
        }
    } catch (error) {
        console.log('❌ Error accessing FileExplorer:', error.message);
    }
    
    // Test 2: Check context menu creation
    console.log('\n2. Testing context menu creation...');
    try {
        const fileExplorer = window.rustCodeApp?.fileExplorer;
        if (fileExplorer && fileExplorer.contextMenu) {
            console.log('✅ Context menu element exists');
            console.log('   Class name:', fileExplorer.contextMenu.className);
            console.log('   Parent element:', fileExplorer.contextMenu.parentElement?.tagName);
        } else {
            console.log('❌ Context menu element not found');
        }
    } catch (error) {
        console.log('❌ Error checking context menu:', error.message);
    }
    
    // Test 3: Check performance metrics
    console.log('\n3. Testing performance metrics...');
    try {
        const fileExplorer = window.rustCodeApp?.fileExplorer;
        if (fileExplorer && fileExplorer.performanceMetrics) {
            console.log('✅ Performance metrics object exists');
            console.log('   Metrics:', fileExplorer.performanceMetrics);
        } else {
            console.log('❌ Performance metrics not found');
        }
    } catch (error) {
        console.log('❌ Error checking performance metrics:', error.message);
    }
    
    // Test 4: Test method calls (safe testing)
    console.log('\n4. Testing method calls...');
    try {
        const fileExplorer = window.rustCodeApp?.fileExplorer;
        if (fileExplorer) {
            // Test updatePerformanceMetrics
            if (typeof fileExplorer.updatePerformanceMetrics === 'function') {
                fileExplorer.updatePerformanceMetrics();
                console.log('✅ updatePerformanceMetrics called successfully');
            }
            
            // Test hideContextMenu
            if (typeof fileExplorer.hideContextMenu === 'function') {
                fileExplorer.hideContextMenu();
                console.log('✅ hideContextMenu called successfully');
            }
            
            // Test throttle function
            if (typeof fileExplorer.throttle === 'function') {
                const throttledFn = fileExplorer.throttle(() => {
                    console.log('✅ Throttled function executed');
                }, 100);
                throttledFn();
                console.log('✅ throttle function created successfully');
            }
        }
    } catch (error) {
        console.log('❌ Error testing method calls:', error.message);
    }
    
    // Test 5: Check for JavaScript errors in console
    console.log('\n5. Monitoring for JavaScript errors...');
    let errorCount = 0;
    const originalError = console.error;
    console.error = function(...args) {
        if (args[0] && args[0].includes && 
            (args[0].includes('updatePerformanceMetrics') || 
             args[0].includes('hideContextMenu'))) {
            errorCount++;
            console.log('❌ Found target error:', args[0]);
        }
        originalError.apply(console, args);
    };
    
    // Wait a bit and check error count
    setTimeout(() => {
        console.error = originalError; // Restore original
        if (errorCount === 0) {
            console.log('✅ No target JavaScript errors detected');
        } else {
            console.log(`❌ Found ${errorCount} target JavaScript errors`);
        }
        
        console.log('\n=== Test Complete ===');
        console.log('File Explorer error fixes have been tested.');
    }, 2000);
}

// Run the test when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testFileExplorerErrors);
} else {
    testFileExplorerErrors();
}

// Export for manual testing
window.testFileExplorerErrors = testFileExplorerErrors;
